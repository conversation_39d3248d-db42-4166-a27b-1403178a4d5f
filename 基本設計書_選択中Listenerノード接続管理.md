# 基本設計書：選択中Listenerノード接続管理

## 1. 概要

### 1.1 目的
BCMonitoringアプリケーションにおいて、Ethereumブロックチェーンノードへの接続を管理し、安定したWebSocket接続を維持するためのListenerノード接続管理機能の設計を定義する。

### 1.2 スコープ
- WebSocket接続の確立と管理
- 接続プールの管理
- 接続エラー時の復旧処理
- ノード選択とフェイルオーバー機能
- 設定管理とモニタリング

## 2. アーキテクチャ概要

### 2.1 システム構成
```
┌─────────────────────────────────────────────────────────────┐
│                    BCMonitoring Application                 │
├─────────────────────────────────────────────────────────────┤
│  MonitorEventService                                        │
│  ├─ EventLogRepositoryImpl                                  │
│  └─ EthEventLogDao                                          │
├─────────────────────────────────────────────────────────────┤
│  Web3jConfig (接続管理)                                      │
│  ├─ WebSocket接続インスタンス管理                             │
│  ├─ 接続プール管理                                           │
│  └─ エラーハンドリング                                       │
├─────────────────────────────────────────────────────────────┤
│  BcmonitoringConfigurationProperties                        │
│  ├─ WebSocket設定                                           │
│  ├─ 環境別設定                                              │
│  └─ タイムアウト設定                                         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Ethereum Node                           │
│  WebSocket Endpoint (ws://host:port)                       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 主要コンポーネント

#### 2.2.1 Web3jConfig
- **役割**: WebSocket接続の作成・管理・破棄
- **機能**:
  - 接続インスタンスのキャッシュ管理
  - 新規接続の作成
  - 接続の正常終了処理

#### 2.2.2 EthEventLogDao
- **役割**: ブロックチェーンイベントの購読管理
- **機能**:
  - WebSocket購読の開始・停止
  - エラー時の接続復旧
  - ブロック処理とイベント配信

#### 2.2.3 MonitorEventService
- **役割**: 監視プロセスの制御
- **機能**:
  - 監視ループの実行
  - エラー時の再起動処理
  - トランザクション処理の調整

## 3. 接続管理詳細設計

### 3.1 接続ライフサイクル

```mermaid
stateDiagram-v2
    [*] --> 未接続
    未接続 --> 接続中 : createWebSocketWeb3j()
    接続中 --> 接続済み : 接続成功
    接続中 --> 接続エラー : 接続失敗
    接続済み --> 購読中 : subscribe()
    購読中 --> 接続済み : unsubscribe()
    購読中 --> 接続エラー : WebSocketエラー
    接続エラー --> 未接続 : shutdownWeb3j()
    接続済み --> 未接続 : shutdownWeb3j()
    接続エラー --> 接続中 : 再接続試行
```

### 3.2 接続プール管理

#### 3.2.1 接続インスタンス管理
```java
public class Web3jConfig {
    private Web3j web3j;           // メイン接続
    private Web3j web3jCaller;     // RPC呼び出し用接続
    
    // 接続取得（キャッシュ利用）
    public synchronized Web3j getWeb3j()
    
    // 新規接続作成
    public synchronized Web3j createWebSocketWeb3j()
    
    // 接続終了
    public synchronized void shutdownWeb3j()
}
```

#### 3.2.2 接続設定
- **接続URI**: `ws://{host}:{port}`
- **自動再接続**: 有効
- **タイムアウト**: 設定可能
- **接続プール**: 2つのインスタンス（購読用・RPC用）

### 3.3 エラーハンドリング

#### 3.3.1 エラー種別と対応
| エラー種別 | 発生箇所 | 対応方法 |
|-----------|---------|---------|
| 接続失敗 | createWebSocketWeb3j() | Web3jConnectionException発生 |
| 購読エラー | subscription.onError | unsubscribe() → shutdownWeb3j() |
| ブロック処理エラー | onNext内 | エラートランザクション送信 |
| タイムアウト | 各種操作 | 接続再作成 |

#### 3.3.2 復旧フロー
```mermaid
flowchart TD
    A[エラー検出] --> B{エラー種別判定}
    B -->|接続エラー| C[unsubscribe実行]
    B -->|処理エラー| D[エラートランザクション送信]
    C --> E[shutdownWeb3j実行]
    E --> F[接続リセット]
    F --> G[再接続試行]
    D --> H[監視継続]
    G --> I[監視再開]
```

## 4. 設定管理

### 4.1 環境別設定

#### 4.1.1 Local環境
```properties
websocket.uri.host=localhost
websocket.uri.port=18541
subscription.check-interval=3000
subscription.allowable-block-timestamp-diff-sec=2
```

#### 4.1.2 Development環境
```properties
websocket.uri.host=${DEV_WEBSOCKET_URI_HOST:dev-websocket.example.com}
websocket.uri.port=${DEV_WEBSOCKET_URI_PORT:8545}
```

#### 4.1.3 Production環境
```properties
websocket.uri.host=${PROD_WEBSOCKET_URI_HOST:mainnet-websocket.example.com}
websocket.uri.port=${PROD_WEBSOCKET_URI_PORT:443}
```

### 4.2 設定クラス構造
```java
@ConfigurationProperties(prefix = "")
public class BcmonitoringConfigurationProperties {
    private Websocket websocket;
    private Subscription subscription;
    
    public static class Websocket {
        private Uri uri;
        
        public static class Uri {
            private String host;
            private String port;
        }
    }
    
    public static class Subscription {
        private String checkInterval;
        private String allowableBlockTimestampDiffSec;
    }
}
```

## 5. 監視とログ

### 5.1 接続状態監視
- 接続成功/失敗のログ出力
- 接続プール使用状況の監視
- WebSocket接続の生存確認

### 5.2 ログレベル
- **DEBUG**: 接続プール操作、接続再利用
- **INFO**: 接続確立、正常終了
- **WARN**: 接続遅延、タイムアウト警告
- **ERROR**: 接続失敗、購読エラー

### 5.3 メトリクス
- 接続数
- 接続失敗回数
- 平均接続時間
- エラー発生率

## 6. 制約事項と考慮事項

### 6.1 制約事項
- 同時接続数: 最大2接続（購読用・RPC用）
- 接続プロトコル: WebSocketのみ
- 単一ノード接続（マルチノード対応なし）

### 6.2 今後の拡張検討事項
- 複数ノードへの接続とロードバランシング
- 接続プールサイズの動的調整
- ヘルスチェック機能の追加
- 接続品質メトリクスの収集

## 7. 関連ドキュメント
- Web3j公式ドキュメント
- Spring Boot設定リファレンス
- Ethereum WebSocket API仕様
